<div>
    <?php if(session()->has('message')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo e(session('message')); ?>

        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <form wire:submit.prevent="savePrescription">
        <?php if (isset($component)) { $__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.row','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout.row'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
            <div class="col">
                
                <?php if (isset($component)) { $__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card.body','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('card.body'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                    <div class="row">
                        <!-- Left Column -->
                        <div class="col-md-6">
                            

                            <!-- Script Date -->
                            <div class="mb-3">
                                <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Script Date','labelRequired' => '1','model' => 'importFile.script_date','id' => 'script_date','type' => 'date']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Script Date','labelRequired' => '1','model' => 'importFile.script_date','id' => 'script_date','type' => 'date']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                                
                            </div>


                            <!-- Last Name -->
                            <div class="mb-3">
                                <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Last Name','labelRequired' => '1','model' => 'importFile.last_name']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Last Name','labelRequired' => '1','model' => 'importFile.last_name']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                            </div>

                            <!-- First Name -->
                            <div class="mb-3">
                                <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'First Name','labelRequired' => '1','model' => 'importFile.first_name']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'First Name','labelRequired' => '1','model' => 'importFile.first_name']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                            </div>

                            <!-- Date of Birth -->
                            <div class="mb-3">
                                <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Date of Birth','labelRequired' => '1','model' => 'importFile.dob','type' => 'date']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Date of Birth','labelRequired' => '1','model' => 'importFile.dob','type' => 'date']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                            </div>

                            <!-- Gender -->
                            <div class="mb-3">
                                <label for="gender" class="form-label">Gender</label>
                                <select wire:model="importFile.gender" id="gender" class="form-control gender-select">
                                    <option value="">Select Gender</option>
                                    <option value="M">Male</option>
                                    <option value="F">Female</option>
                                    
                                </select>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="col-md-6">
                            

                            <div class="mb-3">
                                <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Address','labelRequired' => '1','model' => 'importFile.address']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Address','labelRequired' => '1','model' => 'importFile.address']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                            </div>

                            <div class="mb-3">
                                <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'City','labelRequired' => '1','model' => 'importFile.city']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'City','labelRequired' => '1','model' => 'importFile.city']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                            </div>

                            <div class="mb-7">
                                <label for="state" class="form-label">State</label>
                                <select wire:model="importFile.state" id="state" class="form-control gender-select">
                                    <option value="">Select State</option>
                                    <?php $__currentLoopData = $states; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $state): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($state->id); ?>"><?php echo e($state->name); ?>

                                    </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Zip Code','labelRequired' => '1','model' => 'importFile.zip']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Zip Code','labelRequired' => '1','model' => 'importFile.zip']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                            </div>

                            <div class="mb-3">
                                <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Phone','labelRequired' => '0','model' => 'importFile.phone','type' => 'number']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Phone','labelRequired' => '0','model' => 'importFile.phone','type' => 'number']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Medication Info -->
                    <div class="row mt-4">
                        <div class="col-12">
                            
                            <div class="mb-3">
                                <div class="form-group">
                                    <label labelRequired="1" for="medication" class="form-label">
                                        Medication
                                        <span class="text-danger">*</span>
                                    </label>
                                    <select label="medication"
                                        class="form-control <?php $__errorArgs = ['importFile.medication'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> gender-select"
                                        wire:model="importFile.medication" placeholder="Select medication">
                                        <option value="" selected>Select medication</option>
                                        <?php $__currentLoopData = $medications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $medication): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($medication->name); ?>"><?php echo e($medication->name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>

                                    <?php $__errorArgs = ['importFile.medication'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="form-text text-danger"><strong><?php echo e($message); ?></strong></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="mb-3">
                                <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Strength','labelRequired' => '1','model' => 'importFile.stregnth']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Strength','labelRequired' => '1','model' => 'importFile.stregnth']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                            </div>

                            <div class="mb-3">
                                <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Dosing','labelRequired' => '1','model' => 'importFile.dosing']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Dosing','labelRequired' => '1','model' => 'importFile.dosing']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                            </div>

                            <div class="mb-3">
                                <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Refills','labelRequired' => '0','model' => 'importFile.refills']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Refills','labelRequired' => '0','model' => 'importFile.refills']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                            </div>

                            <div class="mb-3">
                                <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Vial Quantity','labelRequired' => '0','model' => 'importFile.vial_quantity']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Vial Quantity','labelRequired' => '0','model' => 'importFile.vial_quantity']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <input type="hidden" id="comment" value="<?php echo e($importFile->comment); ?>">
                    <input type="hidden" id="operator_name" value="<?php echo e($importFile->returnedByUser ? $importFile->returnedByUser->first_name . ' ' . $importFile->returnedByUser->last_name : ''); ?>">
                    <input type="hidden" id="operator_email" value="<?php echo e($importFile->returnedByUser ? $importFile->returnedByUser->email : ''); ?>">

                    <!-- Additional Information -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            
                            <div class="mb-3">
                                <?php if (isset($component)) { $__componentOriginale04d29a08d4bf79b30ca9866e1e5794c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale04d29a08d4bf79b30ca9866e1e5794c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.textarea','data' => ['label' => 'SIG','labelRequired' => '0','model' => 'importFile.sig']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.textarea'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'SIG','labelRequired' => '0','model' => 'importFile.sig']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale04d29a08d4bf79b30ca9866e1e5794c)): ?>
<?php $attributes = $__attributesOriginale04d29a08d4bf79b30ca9866e1e5794c; ?>
<?php unset($__attributesOriginale04d29a08d4bf79b30ca9866e1e5794c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale04d29a08d4bf79b30ca9866e1e5794c)): ?>
<?php $component = $__componentOriginale04d29a08d4bf79b30ca9866e1e5794c; ?>
<?php unset($__componentOriginale04d29a08d4bf79b30ca9866e1e5794c); ?>
<?php endif; ?>
                            </div>
                        </div>

                        <div class="col-md-6">
                            
                            <div class="mb-3">
                                <?php if (isset($component)) { $__componentOriginale04d29a08d4bf79b30ca9866e1e5794c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale04d29a08d4bf79b30ca9866e1e5794c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.textarea','data' => ['label' => 'Note','labelRequired' => '0','model' => 'importFile.notes','rows' => '3']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.textarea'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Note','labelRequired' => '0','model' => 'importFile.notes','rows' => '3']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale04d29a08d4bf79b30ca9866e1e5794c)): ?>
<?php $attributes = $__attributesOriginale04d29a08d4bf79b30ca9866e1e5794c; ?>
<?php unset($__attributesOriginale04d29a08d4bf79b30ca9866e1e5794c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale04d29a08d4bf79b30ca9866e1e5794c)): ?>
<?php $component = $__componentOriginale04d29a08d4bf79b30ca9866e1e5794c; ?>
<?php unset($__componentOriginale04d29a08d4bf79b30ca9866e1e5794c); ?>
<?php endif; ?>
                            </div>
                        </div>
                    </div>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6)): ?>
<?php $attributes = $__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6; ?>
<?php unset($__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6)): ?>
<?php $component = $__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6; ?>
<?php unset($__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginal2f4c20c75f25d521b59e2ab32e77183d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2f4c20c75f25d521b59e2ab32e77183d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.group.errors','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('group.errors'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2f4c20c75f25d521b59e2ab32e77183d)): ?>
<?php $attributes = $__attributesOriginal2f4c20c75f25d521b59e2ab32e77183d; ?>
<?php unset($__attributesOriginal2f4c20c75f25d521b59e2ab32e77183d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2f4c20c75f25d521b59e2ab32e77183d)): ?>
<?php $component = $__componentOriginal2f4c20c75f25d521b59e2ab32e77183d; ?>
<?php unset($__componentOriginal2f4c20c75f25d521b59e2ab32e77183d); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card.footer','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('card.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                    <?php if($importFile->id): ?>
                    <button type="submit" class="btn btn-primary px-5" wire:loading.attr="disabled"
                        wire:target="savePrescription">
                        <span wire:loading.remove wire:target="saveEditedPrescription">Save</span>
                        <span wire:loading wire:target="saveEditedPrescription" style="display: none;">
                            <i class="fas fa-spinner fa-spin mr-1"></i>
                            Saving...
                        </span>
                    </button>
                    <button type="button" class="btn btn-primary px-5" wire:click="saveAndSignPrescription"
                        wire:loading.attr="disabled" wire:target="saveAndSignPrescription">
                        <span wire:loading.remove wire:target="saveAndSignPrescription">Save & Sign</span>
                        <span wire:loading wire:target="saveAndSignPrescription" style="display: none;">
                            <i class="fas fa-spinner fa-spin mr-1"></i>
                            Saving...
                        </span>
                    </button>
                    <?php else: ?>
                    <button type="submit" class="btn btn-primary px-5" wire:loading.attr="disabled"
                        wire:target="savePrescription">
                        <span wire:loading.remove wire:target="savePrescription">Save</span>
                        <span wire:loading wire:target="savePrescription" style="display: none;">
                            <i class="fas fa-spinner fa-spin mr-1"></i>
                            Saving...
                        </span>
                    </button>
                    <?php endif; ?>

                    <a href="<?php echo e(route('scripts.ready-to-sign')); ?>" class="btn btn-outline-secondary px-5">Cancel</a>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0)): ?>
<?php $attributes = $__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0; ?>
<?php unset($__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0)): ?>
<?php $component = $__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0; ?>
<?php unset($__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0); ?>
<?php endif; ?>
                
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c)): ?>
<?php $attributes = $__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c; ?>
<?php unset($__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c)): ?>
<?php $component = $__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c; ?>
<?php unset($__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c); ?>
<?php endif; ?>
    </form>

</div>

<?php $__env->startPush('scripts'); ?>
<!-- Select2 CSS & JS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
    const comment = $('#comment').val();
    const operatorName = $('#operator_name').val();
    const operatorEmail = $('#operator_email').val();

    if (comment) {
        // Display comment in red alert container
        let commentHtml = `<div class="alert alert-danger mb-3 p-3">
  <div class="w-100 text-break" style="word-break: break-word;">
    <strong>Comment:</strong> ${comment}
  </div>
</div>`;

        // Add operator information below without container if available
        if (operatorName && operatorEmail) {
            commentHtml += `<div class="text-right mb-3">
  <span><strong>${operatorName}</strong> - ${operatorEmail}</span>
</div>`;
        }

        $('.card-header').html(commentHtml);
    }
    document.addEventListener("livewire:load", function() {
        initGenderSelect();

        Livewire.hook('message.processed', () => {
            initGenderSelect();
        });

        function initGenderSelect() {
            let $select = $('.gender-select');

            // Destroy previous instance to avoid duplicates
            $select.select2('destroy');

            // Re-initialize
            $select.select2({
                dropdownParent: $select.parent(), // keeps dropdown below
                dropdownPosition: 'below',
                placeholder: "Select Gender",
                minimumResultsForSearch: Infinity // hides search box
            });
        }

        // Also set default date after Livewire updates (for new records)
        Livewire.hook('message.processed', () => {
            setDefaultScriptDate();
        });
    });
</script>

<style>
    .select2-container--default .select2-selection--single {
        display: flex;
        align-items: center;
        height: 38px;
        /* match your input height */
    }

    .select2-selection__rendered {
        line-height: 1.6 !important;
    }
</style>
<?php $__env->stopPush(); ?><?php /**PATH C:\KodeCreators\newlife-panel\resources\views/livewire/new-import.blade.php ENDPATH**/ ?>
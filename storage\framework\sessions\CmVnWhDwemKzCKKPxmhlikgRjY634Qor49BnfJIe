a:13:{s:6:"_token";s:40:"Eo3V6h9aGp8LIWolZFaqg3GfBeDmEPLAQk4mPpTq";s:6:"_flash";a:2:{s:3:"old";a:0:{}s:3:"new";a:0:{}}s:9:"_previous";a:1:{s:3:"url";s:34:"http://localhost:8000/excel-import";}s:3:"url";a:0:{}s:50:"login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d";i:4;s:4:"auth";a:1:{s:21:"password_confirmed_at";i:1748928629;}s:11:"device_time";s:25:"2025-06-03 14:31:48 +0630";s:21:"all_prescription_data";a:12:{i:0;a:16:{i:0;N;i:1;N;i:2;N;i:3;N;i:4;N;i:5;N;i:6;N;i:7;N;i:8;N;i:9;N;i:10;N;i:11;N;i:12;N;i:13;N;i:14;N;i:15;N;}i:1;a:16:{i:0;N;i:1;N;i:2;N;i:3;N;i:4;N;i:5;N;i:6;N;i:7;N;i:8;N;i:9;N;i:10;N;i:11;N;i:12;N;i:13;N;i:14;N;i:15;N;}i:2;a:16:{i:0;s:11:"Script Date";i:1;s:9:"Last Name";i:2;s:10:"First Name";i:3;s:3:"DOB";i:4;s:6:"Gender";i:5;s:7:"Address";i:6;s:4:"City";i:7;s:5:"State";i:8;s:7:"Zipcode";i:9;s:5:"Phone";i:10;s:10:"Medication";i:11;s:8:"Stregnth";i:12;s:6:"Dosing";i:13;s:7:"Refills";i:14;s:8:"Vial Qty";i:15;s:3:"SIG";}i:3;a:16:{i:0;i:45761;i:1;s:17:"Castillo Dorantes";i:2;s:5:"Lucia";i:3;i:26195;i:4;s:1:"F";i:5;s:19:"14752 Jefferson St.";i:6;s:11:"Midway City";i:7;s:2:"CA";i:8;i:92655;i:9;i:344587797;i:10;s:11:"Semaglutide";i:11;s:22:"2.5mg/4ml (10mg total)";i:12;s:14:"10mg per month";i:13;i:5;i:14;i:6;i:15;i:10;}i:4;a:16:{i:0;i:45761;i:1;s:11:"Kirkpatrick";i:2;s:6:"Brenda";i:3;i:23602;i:4;s:1:"F";i:5;s:16:"8005 San Lus Cir";i:6;s:10:"Buena Park";i:7;s:2:"CA";i:8;i:90620;i:9;i:544587797;i:10;s:11:"Semaglutide";i:11;s:22:"2.5mg/4ml (10mg total)";i:12;s:14:"10mg per month";i:13;i:5;i:14;i:6;i:15;i:10;}i:5;a:16:{i:0;i:45761;i:1;s:5:"House";i:2;s:5:"Brian";i:3;i:27008;i:4;s:1:"M";i:5;s:17:"9452 Greenwich Dr";i:6;s:16:"Huntington Beach";i:7;s:2:"CA";i:8;i:92646;i:9;i:744587797;i:10;s:11:"Semaglutide";i:11;s:22:"2.5mg/4ml (10mg total)";i:12;s:14:"10mg per month";i:13;i:5;i:14;i:6;i:15;i:10;}i:6;a:16:{i:0;i:45761;i:1;s:7:"Johnson";i:2;s:5:"Susan";i:3;i:32654;i:4;s:1:"F";i:5;s:15:"862 Doremere Dr";i:6;s:16:"Huntington Beach";i:7;s:2:"CA";i:8;i:92646;i:9;i:444587797;i:10;s:11:"Semaglutide";i:11;s:22:"2.5mg/4ml (10mg total)";i:12;s:14:"10mg per month";i:13;i:5;i:14;i:6;i:15;i:10;}i:7;a:16:{i:0;i:45761;i:1;s:8:"Reynolds";i:2;s:5:"Laura";i:3;i:30118;i:4;s:1:"F";i:5;s:17:"5112 Robinwood Dr";i:6;s:16:"Huntington Beach";i:7;s:2:"CA";i:8;i:92649;i:9;i:844587797;i:10;s:11:"Semaglutide";i:11;s:22:"2.5mg/4ml (10mg total)";i:12;s:14:"10mg per month";i:13;i:5;i:14;i:6;i:15;i:10;}i:8;a:16:{i:0;i:45761;i:1;s:7:"Forsman";i:2;s:7:"Douglas";i:3;i:21510;i:4;s:1:"M";i:5;s:19:"1027 E. Mayfair Ave";i:6;s:6:"Orange";i:7;s:2:"CA";i:8;i:92867;i:9;i:944587797;i:10;s:11:"Semaglutide";i:11;s:22:"2.5mg/4ml (10mg total)";i:12;s:14:"10mg per month";i:13;i:5;i:14;i:6;i:15;i:10;}i:9;a:16:{i:0;i:45761;i:1;s:9:"Degiacomo";i:2;s:4:"Teri";i:3;i:31545;i:4;s:1:"F";i:5;s:15:"9552 Bickley Dr";i:6;s:16:"Huntington Beach";i:7;s:2:"CA";i:8;i:92646;i:9;i:5444587797;i:10;s:11:"Semaglutide";i:11;s:22:"2.5mg/4ml (10mg total)";i:12;s:14:"10mg per month";i:13;i:5;i:14;i:6;i:15;i:10;}i:10;a:16:{i:0;i:45761;i:1;s:12:"Schoendienst";i:2;s:6:"Martin";i:3;i:23223;i:4;s:1:"M";i:5;s:18:"7791 Sea Breeze Dr";i:6;s:16:"Huntington Beach";i:7;s:2:"CA";i:8;i:92648;i:9;i:45744587797;i:10;s:11:"Semaglutide";i:11;s:22:"2.5mg/4ml (10mg total)";i:12;s:14:"10mg per month";i:13;i:5;i:14;i:6;i:15;i:10;}i:11;a:16:{i:0;i:45761;i:1;s:8:"Geissler";i:2;s:8:"Lorraine";i:3;i:24575;i:4;s:1:"F";i:5;s:19:"9271 Winterwood Cir";i:6;s:16:"Huntington Beach";i:7;s:2:"CA";i:8;i:92646;i:9;i:232344587797;i:10;s:11:"Semaglutide";i:11;s:22:"2.5mg/4ml (10mg total)";i:12;s:14:"10mg per month";i:13;i:5;i:14;i:6;i:15;i:10;}}s:10:"error_rows";a:3:{i:0;i:2;i:1;i:3;i:2;i:4;}s:11:"cell_errors";a:3:{i:2;a:1:{i:0;s:9:"Blank row";}i:3;a:1:{i:0;s:9:"Blank row";}i:4;a:8:{i:0;s:54:"Invalid date format. Please use the mm/dd/yyyy format.";i:3;s:54:"Invalid date format. Please use the mm/dd/yyyy format.";i:4;s:25:"Entry must be 'M' or 'F'.";i:7;s:28:"Must be capital letters only";i:8;s:23:"Invalid ZIP code format";i:9;s:86:"Phone number contains invalid characters. Please use numbers and allowed symbols only.";i:13;s:15:"Must be numeric";i:14;s:15:"Must be numeric";}}s:19:"column_order_errors";a:0:{}s:14:"column_headers";a:16:{i:0;s:8:"Column 1";i:1;s:8:"Column 2";i:2;s:8:"Column 3";i:3;s:8:"Column 4";i:4;s:8:"Column 5";i:5;s:8:"Column 6";i:6;s:8:"Column 7";i:7;s:8:"Column 8";i:8;s:8:"Column 9";i:9;s:9:"Column 10";i:10;s:9:"Column 11";i:11;s:9:"Column 12";i:12;s:9:"Column 13";i:13;s:9:"Column 14";i:14;s:9:"Column 15";i:15;s:9:"Column 16";}s:16:"sig_column_index";i:-1;}
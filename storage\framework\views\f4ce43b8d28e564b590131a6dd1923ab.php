

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const continueBtn = document.getElementById('continue-btn');
        if (continueBtn) {
            continueBtn.addEventListener('click', function() {
                this.disabled = true;
                this.classList.add('processing');
                this.innerHTML = '<span class="spinner-border spinner-border-sm mr-2" role="status" aria-hidden="true"></span> Processing...';
                this.form.submit(); // optional: ensures the form still submits
            });
        }

        // Tooltip init (already in your code)
        if (typeof jQuery !== 'undefined') {
            jQuery('[data-toggle="tooltip"]').tooltip({
                container: 'body',
                html: true
            });
        } else {
            console.error('jQuery is not loaded. Tooltips will not work.');
        }
    });
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                
            <div class="card-body">
                <?php if(session('status')): ?>
                <div class="alert" role="alert">
                    <?php echo e(session('status')); ?>

                </div>
                <?php endif; ?>

                <?php if(session('error')): ?>
                <div class="alert" role="alert">
                    <?php echo e(session('error')); ?>

                </div>
                <?php endif; ?>

                <!-- Progress Steps -->
                <div class="progress-steps-container mb-5">
                    <div class="progress-line">
                        <div class="progress-line-inner" style="width: <?php echo e(($currentStep / $totalSteps) * 100); ?>%;">
                        </div>
                    </div>
                    <div class="progress-steps">
                        <div class="progress-step <?php echo e($currentStep >= 1 ? 'active' : ''); ?>">
                            <div class="step-circle">1</div>
                            <div class="step-label">Upload</div>
                        </div>
                        <div class="progress-step <?php echo e($currentStep >= 2 ? 'active' : ''); ?>">
                            <div class="step-circle">2</div>
                            <div class="step-label">Create</div>
                        </div>
                        <div class="progress-step <?php echo e($currentStep >= 3 ? 'active' : ''); ?>">
                            <div class="step-circle">3</div>
                            <div class="step-label">Sign</div>
                        </div>
                        <div class="progress-step <?php echo e($currentStep >= 4 ? 'active' : ''); ?>">
                            <div class="step-circle">4</div>
                            <div class="step-label">Done</div>
                        </div>
                    </div>
                </div>

                    <!-- Data Summary -->
                    <div class="alert">
                        <strong><?php echo e($totalRows - $errorCount); ?> rows</strong> read successfully,
                        <strong class="text-danger"><?php echo e($errorCount); ?> rows</strong>
                        <span style="color: red"> with errors</span>
                        
                    </div>

                    <!-- Data Preview Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr style="background-color: #000000; color: white;">
                                    <th>#</th>
                                    

                                    <?php $__currentLoopData = $columnHeaders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $columnName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($index < 17): ?>
                                        <th>
                                        <?php echo e($columnName); ?>

                                        </th>
                                        <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $allRowsData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                // Check if any cell in this row has an error
                                $hasError =
                                isset($cellErrors[$index + 2]) && !empty($cellErrors[$index + 2]);

                                // Check if this row has any data
                                $hasData = false;
                                foreach ($row as $cellValue) {
                                if ($cellValue !== null && $cellValue !== '') {
                                $hasData = true;
                                break;
                                }
                                }

                                // Check if this is a blank row (should be marked as error)
                                $isBlankRow = !$hasData;
                                ?>
                                <tr class="<?php echo e($hasError || $isBlankRow ? 'error-row' : ''); ?>">
                                    <td><?php echo e($index + 2); ?></td>

                                    <?php for($i = 0; $i < min(17, count($columnHeaders)); $i++): ?>
                                        <td
                                        class="<?php echo e(isset($cellErrors[$index + 2][$i]) || $isBlankRow ? 'has-error' : ''); ?>">
                                        <?php if(isset($row[$i]) && $row[$i] !== null && $row[$i] !== ''): ?>
                                        <?php if(isset($cellErrors[$index + 2][$i])): ?>
                                        <span class="text-danger font-weight-bold" data-toggle="tooltip"
                                            data-placement="top"
                                            title="Error: <?php echo e($cellErrors[$index + 2][$i]); ?>">
                                            <?php if($i == 0 || $i == 3): ?>
                                            
                                            <?php if(is_numeric($row[$i])): ?>
                                            <?php echo e(\PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($row[$i])->format('m/d/Y')); ?>

                                            <?php else: ?>
                                            <?php echo e($row[$i]); ?>

                                            <?php endif; ?>
                                            <?php else: ?>
                                            <?php echo e($row[$i]); ?>

                                            <?php endif; ?>
                                        </span>
                                        <?php else: ?>
                                        <?php if($i == 0 || $i == 3): ?>
                                        
                                        <?php if(is_numeric($row[$i])): ?>
                                        <?php echo e(\PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($row[$i])->format('m/d/Y')); ?>

                                        <?php else: ?>
                                        <?php echo e($row[$i]); ?>

                                        <?php endif; ?>
                                        <?php else: ?>
                                        <?php echo e($row[$i]); ?>

                                        <?php endif; ?>
                                        <?php endif; ?>
                                        <?php else: ?>
                                        <?php if($isBlankRow && $i == 0): ?>
                                        
                                        <span class="text-danger font-weight-bold" data-toggle="tooltip"
                                            data-placement="top" title="Error: Blank row detected">Blank
                                            row</span>
                                        <?php elseif($hasData): ?>
                                        
                                        <?php if(isset($cellErrors[$index + 2][$i])): ?>
                                        <span class="text-danger font-weight-bold"
                                            data-toggle="tooltip" data-placement="top"
                                            title="Error: <?php echo e($cellErrors[$index + 2][$i]); ?>">missing</span>
                                        <?php else: ?>
                                        <span class="text-danger font-weight-bold"
                                            data-toggle="tooltip" data-placement="top"
                                            title="Error: A required value is missing. Please complete all necessary fields.">missing</span>
                                        <?php endif; ?>
                                        <?php endif; ?>
                                        <?php endif; ?>
                                        </td>
                                        <?php endfor; ?>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="d-flex justify-content-between mt-5">
                        <a href="<?php echo e(route('excel.import')); ?>" class="btn btn-outline-secondary px-4 py-2 back-btn">
                            <i class="fas fa-arrow-left mr-1"></i> Back
                        </a>
                        <form action="<?php echo e(route('excel.process')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="btn btn-danger px-4" <?php echo e($validRows <= 0 ? 'disabled' : ''); ?> id="continue-btn">
                                <span class="continue-text">Continue <i class="fas fa-arrow-right ml-1"></i></span>
                            </button>
                            <?php if($validRows <= 0): ?>
                                <div class="text-danger mt-2">
                                <small>Cannot continue - all rows contain errors</small>
                    </div>
                    <?php endif; ?>
                    </form>
            </div>
        </div>
    </div>
</div>
</div>
</div>

<style>
    .card {
        border: none;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
        border-radius: 0;
    }

    .card-header {
        background-color: #fff;
        border-bottom: 1px solid #f1f1f1;
        padding: 15px 20px;
    }

    /* Progress Steps Styling */
    .progress-steps-container {
        position: relative;
        padding: 20px 0;
        margin: 0 auto;
        max-width: 700px;
    }

    .progress-steps {
        display: flex;
        justify-content: space-between;
        position: relative;
        z-index: 1;
    }

    .progress-step {
        text-align: center;
        width: 20%;
        position: relative;
    }

    .step-circle {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background-color: #e9ecef;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 8px;
        font-weight: 500;
        font-size: 14px;
    }

    .progress-step.active .step-circle {
        background-color: #000000;
        color: white;
    }

    .step-label {
        font-size: 13px;
        color: #6c757d;
        font-weight: 400;
    }

    .progress-step.active .step-label {
        color: #000000;
        font-weight: 500;
    }

    .progress-line {
        position: absolute;
        top: 38px;
        left: 10%;
        right: 10%;
        height: 2px;
        background-color: #e9ecef;
        z-index: 0;
    }

    .progress-line-inner {
        height: 100%;
        background-color: #000000;
    }

    .table.custom-striped tbody tr:not(.error-row):nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.05);
    }

    .table.custom-striped tbody tr:not(.error-row):hover {
        background-color: rgba(0, 0, 0, 0.1);
    }

    .btn-danger {
        background-color: #000000;
        border-color: #000000;
    }

    /* Removed alert-danger style to use our custom alert style */

    /* Cell error styling */
    .bg-danger {
        background-color: #dc3545 !important;
    }

    .bg-danger:hover {
        background-color: #bd2130 !important;
    }

    .small.text-white {
        font-size: 80%;
    }

    /* Error styling */
    .text-danger {
        color: #dc3545 !important;
        cursor: pointer;
    }

    /* Error row styling */
    .error-row {
        background-color: rgba(220, 53, 69, 0.1) !important;
    }

    .error-row:hover {
        background-color: rgba(220, 53, 69, 0.15) !important;
    }

    /* Cell with error styling */
    .has-error {
        position: relative;
        /* background-color: rgba(220, 53, 69, 0.1) !important; */
    }

    /* Make blank row errors more visible */
    .text-danger[title*="Blank row"] {
        font-size: 1.1em;
        font-weight: bold !important;
        color: #dc3545 !important;
    }

    /* Ensure error text is clearly visible */
    .text-danger {
        color: #dc3545 !important;
        font-weight: bold !important;
    }

    /* Back button styling to match Continue button */
    .back-btn {
        font-size: 1rem;
        line-height: 1.5;
        border-width: 1px;
        height: 38px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    /* Custom alert styling */
    .alert {
        background-color: #dff9ff;
        color: #333;
        border: none;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 15px;
    }

    /* Spinner styling */
    .spinner-border-sm {
        width: 1rem;
        height: 1rem;
        border-width: 0.2em;
        vertical-align: middle;
    }

    /* Ensure button maintains its size when showing spinner */
    button[type="submit"] {
        min-width: 120px;
    }
</style>


<?php $__env->stopSection(); ?>
<?php echo $__env->make('master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\KodeCreators\newlife-panel\resources\views/excel-import/preview.blade.php ENDPATH**/ ?>